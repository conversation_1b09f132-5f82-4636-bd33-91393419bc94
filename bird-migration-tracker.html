<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>鸟类迁徙轨迹播放器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            height: 100vh;
        }
        
        #mapDiv {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1;
        }
        
        .floating-panel {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(240, 240, 240, 0.95);
            backdrop-filter: blur(8px);
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            min-width: 280px;
            max-width: 320px;
            border: 1px solid rgba(200, 200, 200, 0.3);
        }
        
        .panel-header {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 10px;
            margin: -15px -15px 10px -15px;
            border-radius: 12px 12px 0 0;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .panel-header h1 {
            font-size: 16px;
            margin-bottom: 2px;
            text-shadow: none;
        }
        
        .panel-header p {
            font-size: 11px;
            opacity: 0.8;
            margin: 0;
        }
        
        .controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 10px;
        }
        
        .control-group label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }
        
        select, input, button {
            padding: 8px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        select:focus, input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        button {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        button:hover {
            background: linear-gradient(135deg, #5a6268, #3d4142);
            box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
        }
        
        button:disabled {
            background: linear-gradient(135deg, #adb5bd, #868e96);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .play-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
        }
        
        .play-btn:hover {
            background: linear-gradient(135deg, #218838, #1c7430);
            box-shadow: 0 3px 12px rgba(40, 167, 69, 0.3);
        }
        
        .pause-btn {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: #212529;
            box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
        }
        
        .pause-btn:hover {
            background: linear-gradient(135deg, #e0a800, #e8590c);
            box-shadow: 0 3px 12px rgba(255, 193, 7, 0.3);
        }
        
        .stop-btn {
            background: linear-gradient(135deg, #dc3545, #c82333);
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
        }
        
        .stop-btn:hover {
            background: linear-gradient(135deg, #c82333, #a02622);
            box-shadow: 0 3px 12px rgba(220, 53, 69, 0.3);
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        .info-panel {
            margin-top: 15px;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .info-label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 13px;
        }
        
        .info-value {
            color: #667eea;
            font-weight: 600;
            font-size: 13px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
            width: 0%;
            border-radius: 3px;
        }
        
        .speed-control {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 10px;
        }
        
        .speed-slider {
            flex: 1;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }
        
        .speed-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            background: linear-gradient(135deg, #6c757d, #495057);
            cursor: pointer;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .speed-slider::-moz-range-thumb {
            width: 16px;
            height: 16px;
            background: linear-gradient(135deg, #6c757d, #495057);
            cursor: pointer;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        @media (max-width: 768px) {
            .floating-panel {
                bottom: 10px;
                left: 10px;
                right: 10px;
                min-width: auto;
                max-width: none;
                padding: 15px;
            }
            
            .panel-header {
                margin: -15px -15px 10px -15px;
                padding: 12px;
            }
            
            .panel-header h1 {
                font-size: 18px;
            }
            
            .controls {
                gap: 10px;
            }
            
            .button-group {
                flex-wrap: wrap;
            }
            
            button {
                flex: 1;
                min-width: 80px;
            }
        }
        
        /* 移除动画效果 */
    </style>
</head>
<body>
    <div id="mapDiv"></div>
    
    <div class="floating-panel">
        <div class="panel-header">
            <h1>🐦 鸟类迁徙轨迹播放器</h1>
            <p>迁徙轨迹可视化</p>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label for="birdName">鸟类名称:</label>
                <input type="text" id="birdName" value="东方斑尾塍鹬" readonly>
            </div>
            
            <div class="control-group">
                <label for="altitude">地图高度:</label>
                <select id="altitude">
                    <option value="1">1 km</option>
                    <option value="5">5 km</option>
                    <option value="10">10 km</option>
                    <option value="50">50 km</option>
                    <option value="100">100 km</option>
                    <option value="500">500 km</option>
                    <option value="1000">1000 km</option>
                </select>
            </div>
            
            <div class="button-group">
                <button id="playBtn" class="play-btn">▶ 播放</button>
                <button id="pauseBtn" class="pause-btn" disabled>⏸ 暂停</button>
                <button id="stopBtn" class="stop-btn" disabled>⏹ 停止</button>
            </div>
            
            <div class="control-group speed-control">
                <label for="speedSlider">播放速度:</label>
                <input type="range" id="speedSlider" class="speed-slider" min="0.5" max="5" step="0.5" value="1">
                <span id="speedValue">1x</span>
            </div>
        </div>
        
        <div class="info-panel">
            <div class="info-row">
                <span class="info-label">当前进度:</span>
                <span class="info-value" id="progressText">0 / 0</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="info-row">
                <span class="info-label">当前位置:</span>
                <span class="info-value" id="currentPosition">--</span>
            </div>
            <div class="info-row">
                <span class="info-label">总距离:</span>
                <span class="info-value" id="totalDistance">--</span>
            </div>
            <div class="info-row">
                <span class="info-label">状态:</span>
                <span class="info-value" id="statusText">准备就绪</span>
            </div>
        </div>
    </div>

    <script src="https://api.tianditu.gov.cn/api?v=4.0&tk=46697f7c9051544d0a2c159dfcedb425"></script>
    <script src="bird-migration-tracker.js"></script>
</body>
</html>