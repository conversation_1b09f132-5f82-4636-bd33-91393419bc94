// 鸟类迁徙轨迹播放器
class BirdMigrationTracker {
    constructor() {
        this.map = null;
        this.trackPoints = [];
        this.currentMarker = null;
        this.trackLine = null;
        this.playbackTimer = null;
        this.currentIndex = 0;
        this.isPlaying = false;
        this.isPaused = false;
        this.playbackSpeed = 1;
        this.totalDistance = 0;
        this.currentTrackSegment = [];
        this.segmentLines = [];
        this.segmentStartTime = null;
        this.totalPlaybackTime = 60000; // 总播放时间60秒
        this.lastMapUpdate = 0;
        this.mapUpdateInterval = 100; // 地图更新间隔（毫秒）
        
        // 简单恒定速度控制 - 提高速度用于演示
        this.baseSpeed = 1000000; // 基础速度：1000km/秒（米/秒）
        
        this.init();
    }
    
    init() {
        this.waitForTiandituAPI();
        this.setupEventListeners();
        this.loadTrackData();
    }
    
    // 等待天地图API加载
    waitForTiandituAPI() {
        let attempts = 0;
        const maxAttempts = 10;
        
        const checkAPI = () => {
            attempts++;
            if (typeof T !== 'undefined') {
                this.initMap();
            } else if (attempts < maxAttempts) {
                setTimeout(checkAPI, 1000);
            } else {
                this.updateStatus('天地图API加载失败', 'error');
            }
        };
        checkAPI();
    }
    
    // 初始化地图
    initMap() {
        try {
            this.map = new T.Map('mapDiv');
            
            // 设置初始中心点和缩放级别
            const center = new T.LngLat(116.40969, 39.89945);
            this.map.centerAndZoom(center, 2);
            
            // 添加地图控件
            const mapTypeCtrl = new T.Control.MapType();
            this.map.addControl(mapTypeCtrl);
            
            const zoomCtrl = new T.Control.Zoom();
            this.map.addControl(zoomCtrl);
            
            const scaleCtrl = new T.Control.Scale();
            this.map.addControl(scaleCtrl);
            
            this.updateStatus('地图初始化成功', 'success');
            
        } catch (error) {
            console.error('地图初始化失败:', error);
            this.updateStatus('地图初始化失败', 'error');
        }
    }
    
    // 设置事件监听器
    setupEventListeners() {
        document.getElementById('playBtn').addEventListener('click', () => this.startPlayback());
        document.getElementById('pauseBtn').addEventListener('click', () => this.pausePlayback());
        document.getElementById('stopBtn').addEventListener('click', () => this.stopPlayback());
        
        const speedSlider = document.getElementById('speedSlider');
        speedSlider.addEventListener('input', (e) => {
            this.playbackSpeed = parseFloat(e.target.value);
            document.getElementById('speedValue').textContent = this.playbackSpeed + 'x';
        });
        
        const altitudeSelect = document.getElementById('altitude');
        altitudeSelect.addEventListener('change', (e) => {
            this.changeAltitude(parseInt(e.target.value));
        });
    }
    
    // 加载轨迹数据
    async loadTrackData() {
        try {
            // 尝试从文件加载轨迹数据
            const response = await fetch('track-data.txt');
            if (response.ok) {
                const text = await response.text();
                this.parseTrackData(text);
            } else {
                // 如果文件不存在，使用默认数据
                this.useDefaultData();
            }
        } catch (error) {
            console.warn('无法加载轨迹数据文件，使用默认数据:', error);
            this.useDefaultData();
        }
    }
    
    // 使用默认数据
    useDefaultData() {
        const defaultData = `东亚-澳大利西亚迁飞通道的东方斑尾塍鹬
175.330000,-37.160000
170.653552,-31.917509
166.489253,-26.522438
162.703213,-21.017411
159.189227,-15.434651
155.860666,-9.799530
152.643904,-4.133073
149.472970,1.546178
146.284983,7.220672
143.015914,12.872490
139.596229,18.481941
135.945958,24.025897
131.968722,29.475626
127.544347,34.793767
122.520000,39.930000
127.192330,44.135617
131.154371,48.007116
134.563281,51.449491
137.582945,54.398805
140.372152,56.831345
143.086671,58.765192
145.765245,60.239740
148.421107,61.301284
-162.000000,61.300000
-165.832445,54.437770
-168.555066,47.489310
-170.636841,40.491559
-172.322940,33.463551
-173.754661,26.416160
-175.020583,19.356166
-176.180894,12.288155
-177.280163,5.215514
-178.354810,-1.858992
-179.438149,-8.932840
179.435423,-16.003421
178.225939,-23.067701
176.881796,-30.121752
175.330000,-37.160000`;
        
        this.parseTrackData(defaultData);
    }
    
    // 解析轨迹数据
    parseTrackData(data) {
        const lines = data.trim().split('\n');
        let birdName = '未知鸟类';
        
        this.trackPoints = [];
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;
            
            // 第一行是鸟类名称
            if (i === 0) {
                birdName = line;
                document.getElementById('birdName').value = birdName;
                continue;
            }
            
            // 解析坐标
            const coords = line.split(',');
            if (coords.length === 2) {
                const lng = parseFloat(coords[0]);
                const lat = parseFloat(coords[1]);
                
                if (!isNaN(lng) && !isNaN(lat)) {
                    this.trackPoints.push({
                        lng: lng,
                        lat: lat,
                        index: this.trackPoints.length
                    });
                }
            }
        }
        
        if (this.trackPoints.length > 0) {
            this.calculateTotalDistance();
            this.updateProgress();
            this.updateStatus(`轨迹数据加载成功，共 ${this.trackPoints.length} 个点`, 'success');
        } else {
            this.updateStatus('轨迹数据格式错误', 'error');
        }
    }
    
    // 计算总距离
    calculateTotalDistance() {
        this.totalDistance = 0;
        for (let i = 1; i < this.trackPoints.length; i++) {
            const prev = this.trackPoints[i - 1];
            const curr = this.trackPoints[i];
            this.totalDistance += this.calculateDistance(prev, curr);
        }
        
        document.getElementById('totalDistance').textContent = 
            this.totalDistance > 1000 ? 
            `${(this.totalDistance / 1000).toFixed(1)} km` : 
            `${this.totalDistance.toFixed(1)} m`;
    }
    
    // 计算两点间距离（米）
    calculateDistance(point1, point2) {
        const R = 6371000; // 地球半径（米）
        const dLat = this.toRadians(point2.lat - point1.lat);
        const dLng = this.toRadians(point2.lng - point1.lng);
        
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                  Math.cos(this.toRadians(point1.lat)) * Math.cos(this.toRadians(point2.lat)) *
                  Math.sin(dLng / 2) * Math.sin(dLng / 2);
        
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }
    
    toRadians(degrees) {
        return degrees * (Math.PI / 180);
    }
    
    // 计算线段需要的插值点数（基于恒定速度）
    calculateInterpolationPoints(segmentDistance) {
        // 基于恒定速度计算需要的总时间
        const segmentDuration = segmentDistance / this.baseSpeed; // 秒
        
        // 每个线段至少30个点，最多200个点
        return Math.min(Math.max(Math.ceil(segmentDuration * 60), 30), 200);
    }
    
    // 显示完整轨迹范围（但不显示轨迹线）
    showTrackBounds() {
        if (!this.map || this.trackPoints.length < 2) return;
        
        try {
            // 调整地图视野以显示完整轨迹范围
            const bounds = new T.LngLatBounds();
            this.trackPoints.forEach(point => {
                bounds.extend(new T.LngLat(point.lng, point.lat));
            });
            this.map.fitBounds(bounds);
            
        } catch (error) {
            console.error('设置地图范围失败:', error);
        }
    }
    
    // 开始播放
    startPlayback() {
        if (this.trackPoints.length === 0) {
            this.updateStatus('没有可播放的轨迹数据', 'error');
            return;
        }
        
        if (this.isPaused) {
            this.isPaused = false;
        } else {
            this.currentIndex = 0;
            this.clearAllTrackLines();
            this.currentTrackSegment = [];
            this.showTrackBounds();
            this.segmentStartTime = Date.now();
        }
        
        this.isPlaying = true;
        this.updateButtonStates();
        this.updateStatus('正在播放轨迹...', 'playing');
        
        this.playNextSegment();
    }
    
    // 播放下一个线段
    playNextSegment() {
        if (!this.isPlaying || this.currentIndex >= this.trackPoints.length - 1) {
            this.stopPlayback();
            return;
        }
        
        const startPoint = this.trackPoints[this.currentIndex];
        const endPoint = this.trackPoints[this.currentIndex + 1];
        
        // 计算当前线段的距离
        const segmentDistance = this.calculateDistance(startPoint, endPoint);
        
        // 基于恒定速度计算需要的插值点数
        const interpolationPoints = this.calculateInterpolationPoints(segmentDistance);
        
        // 基于恒定速度计算实际持续时间
        const segmentDuration = (segmentDistance / this.baseSpeed) / this.playbackSpeed;
        
        // 显示调试信息
        this.showSpeedDebugInfo(segmentDistance, { interpolationPoints, segmentDuration });
        
        // 播放当前线段（使用恒定速度插值）
        this.playSegmentWithConstantSpeed(startPoint, endPoint, segmentDuration, interpolationPoints);
    }
    
    // 播放单个线段（保持原有方法用于兼容性）
    playSegment(startPoint, endPoint, duration, steps = 30) {
        this.playSegmentWithConstantSpeed(startPoint, endPoint, duration, steps);
    }
    
    // 使用固定时间间隔播放单个线段
    playSegmentWithConstantSpeed(startPoint, endPoint, duration, interpolationPoints) {
        let currentPoint = 0;
        const stepDuration = duration * 1000 / interpolationPoints; // 每步的毫秒数
        
        const animateStep = () => {
            if (!this.isPlaying) return;
            
            const progress = currentPoint / interpolationPoints;
            
            // 计算当前位置（线性插值）
            const currentLng = startPoint.lng + (endPoint.lng - startPoint.lng) * progress;
            const currentLat = startPoint.lat + (endPoint.lat - startPoint.lat) * progress;
            
            // 更新位置
            this.updateCurrentPosition({lng: currentLng, lat: currentLat});
            
            // 更新轨迹线
            this.currentTrackSegment.push(new T.LngLat(currentLng, currentLat));
            this.updateTrackLine();
            
            // 更新进度
            const overallProgress = (this.currentIndex + progress) / (this.trackPoints.length - 1);
            this.updateProgressByRatio(overallProgress);
            
            currentPoint++;
            
            if (currentPoint <= interpolationPoints) {
                setTimeout(animateStep, stepDuration);
            } else {
                // 当前线段播放完成，播放下一个
                this.currentIndex++;
                setTimeout(() => this.playNextSegment(), 50);
            }
        };
        
        animateStep();
    }
    
    // 更新当前位置
    updateCurrentPosition(point) {
        if (!this.map) return;
        
        try {
            const lngLat = new T.LngLat(point.lng, point.lat);
            
            // 如果标记不存在，创建新标记
            if (!this.currentMarker) {
                // 创建红色鸟类图标
                const icon = new T.Icon({
                    iconUrl: 'data:image/svg+xml;base64,' + btoa(`
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10" fill="#ff4757" stroke="#ffffff" stroke-width="2"/>
                            <path d="M12 6l-2 6h4l-2 6 2-6h-4l2-6z" fill="#ffffff"/>
                        </svg>
                    `),
                    iconSize: new T.Point(32, 32),
                    iconAnchor: new T.Point(16, 16)
                });
                
                // 创建标记
                this.currentMarker = new T.Marker(lngLat, {icon: icon});
                this.map.addOverLay(this.currentMarker);
            } else {
                // 直接更新现有标记位置
                this.currentMarker.setLngLat(lngLat);
            }
            
            // 地图跟随红点移动，但限制更新频率避免抖动
            const now = Date.now();
            if (now - this.lastMapUpdate > this.mapUpdateInterval) {
                this.map.panTo(lngLat);
                this.lastMapUpdate = now;
            }
            
            // 更新位置信息
            document.getElementById('currentPosition').textContent = 
                `${point.lng.toFixed(6)}, ${point.lat.toFixed(6)}`;
            
        } catch (error) {
            console.error('更新位置失败:', error);
        }
    }
    
    // 更新轨迹线
    updateTrackLine() {
        if (!this.map || this.currentTrackSegment.length < 2) return;
        
        try {
            // 移除当前的轨迹线
            if (this.trackLine) {
                this.map.removeOverLay(this.trackLine);
            }
            
            // 创建新的轨迹线（黄色）
            this.trackLine = new T.Polyline(this.currentTrackSegment, {
                color: '#ffd93d',
                weight: 4,
                opacity: 0.9
            });
            
            this.map.addOverLay(this.trackLine);
            
        } catch (error) {
            console.error('更新轨迹线失败:', error);
        }
    }
    
    // 更新进度（基于比例）
    updateProgressByRatio(ratio) {
        const progress = ratio * 100;
        const currentIndex = Math.floor(ratio * (this.trackPoints.length - 1));
        
        document.getElementById('progressFill').style.width = progress + '%';
        document.getElementById('progressText').textContent = `${currentIndex} / ${this.trackPoints.length}`;
    }
    
    // 暂停播放
    pausePlayback() {
        this.isPaused = true;
        this.isPlaying = false;
        
        if (this.playbackTimer) {
            clearTimeout(this.playbackTimer);
            this.playbackTimer = null;
        }
        
        this.updateButtonStates();
        this.updateStatus('播放已暂停', 'paused');
    }
    
    // 停止播放
    stopPlayback() {
        this.isPlaying = false;
        this.isPaused = false;
        this.currentIndex = 0;
        
        if (this.playbackTimer) {
            clearTimeout(this.playbackTimer);
            this.playbackTimer = null;
        }
        
        // 移除当前位置标记
        if (this.currentMarker) {
            this.map.removeOverLay(this.currentMarker);
            this.currentMarker = null;
        }
        
        // 清除所有轨迹线
        this.clearAllTrackLines();
        this.currentTrackSegment = [];
        
        this.updateButtonStates();
        this.updateProgress();
        this.updateStatus('播放已停止', 'stopped');
    }
    
    // 清除所有轨迹线
    clearAllTrackLines() {
        if (this.trackLine) {
            this.map.removeOverLay(this.trackLine);
            this.trackLine = null;
        }
        
        this.segmentLines.forEach(line => {
            if (line) {
                this.map.removeOverLay(line);
            }
        });
        this.segmentLines = [];
    }
    
    // 更新进度
    updateProgress() {
        const progress = this.trackPoints.length > 1 ? (this.currentIndex / (this.trackPoints.length - 1)) * 100 : 0;
        
        document.getElementById('progressFill').style.width = progress + '%';
        document.getElementById('progressText').textContent = `${this.currentIndex} / ${this.trackPoints.length}`;
    }
    
    // 更新按钮状态
    updateButtonStates() {
        const playBtn = document.getElementById('playBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const stopBtn = document.getElementById('stopBtn');
        
        playBtn.disabled = this.isPlaying;
        pauseBtn.disabled = !this.isPlaying;
        stopBtn.disabled = !this.isPlaying && !this.isPaused;
    }
    
    // 更新状态
    updateStatus(message, type = 'info') {
        const statusElement = document.getElementById('statusText');
        statusElement.textContent = message;
        
        // 根据类型设置颜色
        statusElement.style.color = {
            'success': '#28a745',
            'error': '#dc3545',
            'warning': '#ffc107',
            'playing': '#007bff',
            'paused': '#ffc107',
            'stopped': '#6c757d',
            'info': '#17a2b8'
        }[type] || '#17a2b8';
    }
    
    // 改变高度
    changeAltitude(km) {
        if (!this.map) return;
        
        // 根据高度调整缩放级别
        const zoomLevels = {
            1: 18,
            5: 16,
            10: 14,
            50: 12,
            100: 10,
            500: 8,
            1000: 6
        };
        
        const zoomLevel = zoomLevels[km] || 10;
        this.map.setZoom(zoomLevel);
        
        this.updateStatus(`地图高度已调整为 ${km} km`, 'success');
    }
    
    // 显示速度调试信息
    showSpeedDebugInfo(segmentDistance, params) {
        const speed = this.baseSpeed / this.playbackSpeed; // 当前实际速度（米/秒）
        const info = `距离: ${(segmentDistance/1000).toFixed(0)}km | 速度: ${(speed/1000).toFixed(1)}km/s | 插值点: ${params.interpolationPoints} | 时长: ${params.segmentDuration.toFixed(1)}s`;
        
        // 如果有调试元素则更新，否则创建
        let debugElement = document.getElementById('speedDebug');
        if (!debugElement) {
            debugElement = document.createElement('div');
            debugElement.id = 'speedDebug';
            debugElement.style.cssText = 'position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 5px 10px; border-radius: 5px; font-size: 12px; z-index: 1001;';
            document.body.appendChild(debugElement);
        }
        
        debugElement.textContent = info;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.birdTracker = new BirdMigrationTracker();
});