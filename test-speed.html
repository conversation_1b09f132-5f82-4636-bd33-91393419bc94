<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轨迹速度测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .distance-info {
            margin: 10px 0;
            padding: 10px;
            background: #e8f4f8;
            border-radius: 4px;
        }
        .parameter-info {
            margin: 10px 0;
            padding: 10px;
            background: #f0f8e8;
            border-radius: 4px;
        }
        .launch-btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        .launch-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>🎯 轨迹速度优化测试</h1>
        
        <div class="parameter-info">
            <h3>📊 恒定速度参数</h3>
            <p><strong>基础速度：</strong>50km/秒</p>
            <p><strong>动画帧率：</strong>60fps</p>
            <p><strong>最小插值点：</strong>30点</p>
        </div>
        
        <div class="distance-info">
            <h3>🚀 恒定速度策略</h3>
            <p><strong>所有距离：</strong>基于恒定速度计算插值点</p>
            <p><strong>近距离：</strong>最少30个插值点</p>
            <p><strong>远距离：</strong>根据距离计算所需插值点</p>
        </div>
        
        <p><strong>📝 说明：</strong>这次修改实现了真正的恒定速度移动，无论两点距离多远，移动速度都保持恒定的50km/秒。</p>
        
        <button class="launch-btn" onclick="window.open('bird-migration-tracker.html', '_blank')">🌍 启动轨迹播放器</button>
    </div>
    
    <script>
        // 计算测试数据的距离
        const testPoints = [
            {lng: -118.4085, lat: 33.9416, name: "洛杉矶"},
            {lng: -119.5000, lat: 35.0000, name: "加州中部"},
            {lng: -166.0000, lat: 53.0000, name: "阿拉斯加"}
        ];
        
        function calculateDistance(point1, point2) {
            const R = 6371000; // 地球半径（米）
            const dLat = (point2.lat - point1.lat) * Math.PI / 180;
            const dLng = (point2.lng - point1.lng) * Math.PI / 180;
            
            const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                      Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) *
                      Math.sin(dLng / 2) * Math.sin(dLng / 2);
            
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            return R * c;
        }
        
        // 显示测试距离
        let distanceHtml = '<h3>📍 测试数据距离分析（恒定速度）</h3>';
        const baseSpeed = 50000; // 50km/s
        const frameRate = 60; // 60fps
        
        for (let i = 0; i < testPoints.length - 1; i++) {
            const distance = calculateDistance(testPoints[i], testPoints[i + 1]);
            const duration = distance / baseSpeed;
            const interpolationPoints = Math.max(Math.ceil(duration * frameRate), 30);
            
            distanceHtml += `
                <div style="margin: 10px 0; padding: 10px; background: #e6f3ff; border-radius: 4px;">
                    <strong>${testPoints[i].name} → ${testPoints[i + 1].name}</strong><br>
                    距离: ${(distance/1000).toFixed(0)}km<br>
                    速度: ${(baseSpeed/1000).toFixed(1)}km/s<br>
                    持续时间: ${duration.toFixed(1)}s<br>
                    插值点: ${interpolationPoints}
                </div>
            `;
        }
        
        document.querySelector('.test-info').innerHTML += distanceHtml;
    </script>
</body>
</html>