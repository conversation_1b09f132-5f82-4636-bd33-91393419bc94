# 天地图JavaScript API接入开发指南

## 📋 概述

本文档详细说明如何在Web应用中集成天地图JavaScript API 4.0，实现设备位置的实时显示和地图交互功能。

## 🔑 API密钥配置

### 密钥类型说明
- **服务器端密钥**: `2269e63e4e238ea63aa27542dd2e54d4` - 用于服务器端API调用
- **浏览器端密钥**: `46697f7c9051544d0a2c159dfcedb425` - 用于前端JavaScript API

⚠️ **重要**: JavaScript API必须使用浏览器端密钥，使用服务器端密钥会导致地图瓦片无法加载！

### 申请新密钥
1. 访问 [天地图开发者平台](http://lbs.tianditu.gov.cn/)
2. 注册并登录账号
3. 创建应用，选择"浏览器端"类型
4. 获取API Key

## 🚀 快速开始

### 1. 引入天地图API

```html
<!-- 在HTML头部引入天地图JavaScript API -->
<script type="text/javascript" src="https://api.tianditu.gov.cn/api?v=4.0&tk=YOUR_BROWSER_KEY"></script>
```

### 2. 创建地图容器

```html
<div id="mapDiv" style="width: 100%; height: 500px;"></div>
```

### 3. 基础地图初始化

```javascript
// 全局变量
let map = null;
let marker = null;

// 初始化天地图
function initMap() {
    try {
        // 创建地图实例
        map = new T.Map('mapDiv');

        // 设置地图中心点和缩放级别
        const center = new T.LngLat(116.40969, 39.89945); // 北京天安门
        map.centerAndZoom(center, 18); // 18级约为100米视角

        // 添加地图控件
        const mapTypeCtrl = new T.Control.MapType();
        map.addControl(mapTypeCtrl);

        const zoomCtrl = new T.Control.Zoom();
        map.addControl(zoomCtrl);

        const scaleCtrl = new T.Control.Scale();
        map.addControl(scaleCtrl);

        console.log('天地图初始化成功');
    } catch (error) {
        console.error('天地图初始化失败:', error);
    }
}

// 等待API加载完成
function waitForTiandituAPI() {
    let attempts = 0;
    const maxAttempts = 10;

    function checkAPI() {
        attempts++;
        if (typeof T !== 'undefined') {
            initMap();
        } else if (attempts < maxAttempts) {
            setTimeout(checkAPI, 1000);
        } else {
            console.error('天地图API加载失败');
        }
    }
    checkAPI();
}

// 页面加载完成后初始化
window.onload = function() {
    waitForTiandituAPI();
};
```

## 📍 位置标记功能

### 添加设备位置标记

```javascript
function updateMapLocation(longitude, latitude, data) {
    if (!map || !longitude || !latitude) return;

    try {
        // 创建位置点
        const lngLat = new T.LngLat(longitude, latitude);

        // 移除旧标记
        if (marker) {
            map.removeOverLay(marker);
        }

        // 创建红色圆点图标
        const icon = new T.Icon({
            iconUrl: 'data:image/svg+xml;base64,' + btoa(`
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                    <circle cx="8" cy="8" r="6" fill="#e74c3c" stroke="white" stroke-width="2"/>
                </svg>
            `),
            iconSize: new T.Point(16, 16),
            iconAnchor: new T.Point(8, 8)
        });

        // 创建标记
        marker = new T.Marker(lngLat, {icon: icon});
        map.addOverLay(marker);

        // 移动地图中心（保持当前缩放级别）
        map.panTo(lngLat);

    } catch (error) {
        console.error('更新位置标记失败:', error);
    }
}
```

### 坐标格式转换

```javascript
function convertCoordinate(coord_str) {
    /**
     * 将度分格式转换为十进制度格式
     * 例如: 11957.64581 = 119度57.64581分 = 119.960764度
     */
    try {
        const coord_float = parseFloat(coord_str);

        if (coord_float > 180) { // 度分格式
            const degrees = Math.floor(coord_float / 100);
            const minutes = coord_float - (degrees * 100);
            const decimal_degrees = degrees + (minutes / 60);
            return Math.round(decimal_degrees * 1000000) / 1000000; // 保留6位小数
        } else {
            return coord_float; // 已经是十进制度格式
        }
    } catch (error) {
        console.error('坐标转换失败:', error);
        return null;
    }
}
```

## 🎛️ 地图控件说明

### 缩放级别对应关系
- 级别 1-5: 国家/大洲级别
- 级别 6-10: 省份/城市级别
- 级别 11-15: 区县/街道级别
- 级别 16-18: 建筑物级别（推荐用于设备定位）
- 级别 19-20: 最高精度

### 常用控件

```javascript
// 地图类型控件（卫星图/地形图切换）
const mapTypeCtrl = new T.Control.MapType();
map.addControl(mapTypeCtrl);

// 缩放控件
const zoomCtrl = new T.Control.Zoom();
map.addControl(zoomCtrl);

// 比例尺控件
const scaleCtrl = new T.Control.Scale();
map.addControl(scaleCtrl);

// 鹰眼控件
const overviewCtrl = new T.Control.Overview();
map.addControl(overviewCtrl);
```

## 🔧 常见问题解决

### 1. 地图显示空白/灰色方块
**原因**: 使用了错误的API密钥类型
**解决**: 确保使用浏览器端密钥，不是服务器端密钥

### 2. 地图缩放后自动重置
**原因**: 使用了 `centerAndZoom()` 方法
**解决**: 使用 `panTo()` 方法只移动中心点

```javascript
// ❌ 错误：会重置缩放级别
map.centerAndZoom(lngLat, 15);

// ✅ 正确：保持当前缩放级别
map.panTo(lngLat);
```

### 3. API加载失败
**解决方案**: 实现重试机制

```javascript
function waitForTiandituAPI() {
    let attempts = 0;
    const maxAttempts = 10;

    function checkAPI() {
        attempts++;
        if (typeof T !== 'undefined') {
            initMap();
        } else if (attempts < maxAttempts) {
            setTimeout(checkAPI, 1000);
        } else {
            // 使用备用方案或显示错误信息
            showErrorMessage('天地图API加载失败');
        }
    }
    checkAPI();
}
```

## 📱 响应式设计

```css
/* 地图容器样式 */
.map-container {
    width: 100%;
    height: 500px;
    position: relative;
}

#mapDiv {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .map-container {
        height: 300px;
    }
}
```

## 🔗 相关链接

- [天地图官方文档](http://lbs.tianditu.gov.cn/api/js4.0/guide.html)
- [天地图开发者平台](http://lbs.tianditu.gov.cn/)
- [JavaScript API参考手册](http://lbs.tianditu.gov.cn/api/js4.0/reference.html)

## 📝 开发建议

1. **密钥管理**: 将API密钥存储在环境变量中，不要硬编码
2. **错误处理**: 实现完善的错误处理和备用方案
3. **性能优化**: 避免频繁创建/销毁标记，复用对象
4. **用户体验**: 添加加载状态提示和错误信息显示
5. **测试**: 在不同网络环境下测试API加载稳定性

## 🚀 高级功能示例

### 实时位置更新

```javascript
// 自动更新位置数据
function startAutoUpdate() {
    updateInterval = setInterval(() => {
        fetch('/api/latest_location')
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    const data = result.data;
                    // 转换坐标格式
                    const longitude = convertCoordinate(data.longitude);
                    const latitude = convertCoordinate(data.latitude);

                    if (longitude && latitude) {
                        updateMapLocation(longitude, latitude, data);
                        updateLocationDisplay(data);
                    }
                } else {
                    clearLocationDisplay();
                }
            })
            .catch(error => {
                console.error('获取位置数据失败:', error);
            });
    }, 5000); // 每5秒更新一次
}

function stopAutoUpdate() {
    if (updateInterval) {
        clearInterval(updateInterval);
        updateInterval = null;
    }
}
```

### 轨迹绘制功能

```javascript
let trackLine = null;
let trackPoints = [];

function drawTrack(points) {
    try {
        // 移除旧轨迹
        if (trackLine) {
            map.removeOverLay(trackLine);
        }

        // 转换坐标点
        const lngLats = points.map(point =>
            new T.LngLat(point.longitude, point.latitude)
        );

        // 创建轨迹线
        trackLine = new T.Polyline(lngLats, {
            color: '#3498db',
            weight: 3,
            opacity: 0.8
        });

        map.addOverLay(trackLine);

        // 调整地图视野以显示完整轨迹
        if (lngLats.length > 0) {
            const bounds = new T.LngLatBounds();
            lngLats.forEach(point => bounds.extend(point));
            map.fitBounds(bounds);
        }

    } catch (error) {
        console.error('绘制轨迹失败:', error);
    }
}
```

### 信息窗口（可选）

```javascript
function showInfoWindow(lngLat, data) {
    const infoWin = new T.InfoWindow();
    const content = `
        <div style="padding: 10px; font-size: 12px;">
            <h4 style="margin: 0 0 8px 0; color: #2c3e50;">设备位置</h4>
            <p><strong>经度:</strong> ${data.longitude}</p>
            <p><strong>纬度:</strong> ${data.latitude}</p>
            <p><strong>时间:</strong> ${data.time}</p>
            <p><strong>GPS状态:</strong> ${data.gps_status}</p>
            <p><strong>ICCID:</strong> ${data.iccid}</p>
        </div>
    `;
    infoWin.setContent(content);
    infoWin.setLngLat(lngLat);
    map.addOverLay(infoWin);
}
```

### 地图事件处理

```javascript
function setupMapEvents() {
    // 地图点击事件
    map.addEventListener('click', function(e) {
        console.log('点击位置:', e.lnglat.getLng(), e.lnglat.getLat());
    });

    // 地图缩放事件
    map.addEventListener('zoomend', function(e) {
        console.log('当前缩放级别:', map.getZoom());
    });

    // 标记点击事件
    if (marker) {
        marker.addEventListener('click', function(e) {
            console.log('标记被点击');
        });
    }
}
```

## 🛠️ 完整示例代码

### HTML结构

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>天地图设备定位</title>
    <style>
        .map-container { width: 100%; height: 500px; position: relative; }
        #mapDiv { width: 100%; height: 100%; }
        .control-panel { padding: 10px; background: #f8f9fa; }
        .btn { padding: 8px 16px; margin-right: 10px; }
        .status-text { margin-left: 10px; color: #666; }
        .raw-data { font-family: monospace; font-size: 12px; color: #999; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="control-panel">
        <button class="btn" onclick="refreshLocation()">刷新位置</button>
        <span id="statusText" class="status-text">正在加载地图...</span>
        <div id="rawDataDisplay" class="raw-data"></div>
    </div>

    <div class="map-container">
        <div id="mapDiv"></div>
    </div>

    <script src="https://api.tianditu.gov.cn/api?v=4.0&tk=YOUR_BROWSER_KEY"></script>
    <script src="your-map-script.js"></script>
</body>
</html>
```

### 完整JavaScript代码

```javascript
// 全局变量
let map = null;
let marker = null;
let updateInterval = null;

// 主要功能函数
function initMap() { /* 见上文基础初始化代码 */ }
function updateMapLocation(lng, lat, data) { /* 见上文位置更新代码 */ }
function convertCoordinate(coord_str) { /* 见上文坐标转换代码 */ }
function startAutoUpdate() { /* 见上文自动更新代码 */ }

// 页面初始化
window.onload = function() {
    waitForTiandituAPI();
};

// 手动刷新位置
function refreshLocation() {
    fetch('/api/latest_location')
        .then(response => response.json())
        .then(result => {
            if (result.status === 'success') {
                const data = result.data;
                const longitude = convertCoordinate(data.longitude);
                const latitude = convertCoordinate(data.latitude);

                if (longitude && latitude) {
                    updateMapLocation(longitude, latitude, data);
                    document.getElementById('statusText').textContent = '位置已更新';
                    document.getElementById('rawDataDisplay').textContent = data.raw_data || '';
                }
            }
        })
        .catch(error => {
            console.error('刷新位置失败:', error);
            document.getElementById('statusText').textContent = '获取位置失败';
        });
}
```

## 📊 性能优化建议

1. **标记复用**: 避免频繁创建/销毁标记对象
2. **事件节流**: 对频繁触发的事件进行节流处理
3. **内存管理**: 及时清理不需要的覆盖物和事件监听器
4. **网络优化**: 合理设置更新频率，避免过于频繁的API调用

```javascript
// 节流函数示例
function throttle(func, delay) {
    let timeoutId;
    let lastExecTime = 0;
    return function (...args) {
        const currentTime = Date.now();
        if (currentTime - lastExecTime > delay) {
            func.apply(this, args);
            lastExecTime = currentTime;
        } else {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => {
                func.apply(this, args);
                lastExecTime = Date.now();
            }, delay - (currentTime - lastExecTime));
        }
    };
}

// 使用节流的位置更新
const throttledUpdate = throttle(updateMapLocation, 1000);
```

---

*最后更新: 2025-07-22*
*版本: v1.0*
